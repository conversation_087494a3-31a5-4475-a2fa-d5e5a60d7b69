#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_1
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_3
ADC1.Channel-4\#ChannelRegularConversion=ADC_CHANNEL_8
ADC1.Channel-5\#ChannelRegularConversion=ADC_CHANNEL_9
ADC1.Channel-6\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.Channel-7\#ChannelRegularConversion=ADC_CHANNEL_11
ADC1.Channel-8\#ChannelRegularConversion=ADC_CHANNEL_12
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T4_CC4
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,master,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversionFlag,ScanConvMode,ExternalTrigConv,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,Rank-3\#ChannelRegularConversion,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,Rank-4\#ChannelRegularConversion,Channel-4\#ChannelRegularConversion,SamplingTime-4\#ChannelRegularConversion,Rank-5\#ChannelRegularConversion,Channel-5\#ChannelRegularConversion,SamplingTime-5\#ChannelRegularConversion,Rank-6\#ChannelRegularConversion,Channel-6\#ChannelRegularConversion,SamplingTime-6\#ChannelRegularConversion,Rank-7\#ChannelRegularConversion,Channel-7\#ChannelRegularConversion,SamplingTime-7\#ChannelRegularConversion,Rank-8\#ChannelRegularConversion,Channel-8\#ChannelRegularConversion,SamplingTime-8\#ChannelRegularConversion,NbrOfConversion
ADC1.NbrOfConversion=8
ADC1.NbrOfConversionFlag=1
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.Rank-2\#ChannelRegularConversion=2
ADC1.Rank-3\#ChannelRegularConversion=3
ADC1.Rank-4\#ChannelRegularConversion=4
ADC1.Rank-5\#ChannelRegularConversion=5
ADC1.Rank-6\#ChannelRegularConversion=6
ADC1.Rank-7\#ChannelRegularConversion=7
ADC1.Rank-8\#ChannelRegularConversion=8
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-4\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-5\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-6\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-7\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-8\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.ScanConvMode=ENABLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.RequestsNb=1
FREERTOS.IPParameters=Tasks01
FREERTOS.Tasks01=defaultTask,24,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407ZET6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DMA
Mcu.IP2=FREERTOS
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM4
Mcu.IPNb=7
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PF8
Mcu.Pin11=PF9
Mcu.Pin12=PF10
Mcu.Pin13=PH0-OSC_IN
Mcu.Pin14=PH1-OSC_OUT
Mcu.Pin15=PC0
Mcu.Pin16=PC1
Mcu.Pin17=PC2
Mcu.Pin18=PA1
Mcu.Pin19=PA2
Mcu.Pin2=PF0
Mcu.Pin20=PA3
Mcu.Pin21=PC5
Mcu.Pin22=PB0
Mcu.Pin23=PB1
Mcu.Pin24=PD8
Mcu.Pin25=PD9
Mcu.Pin26=PD10
Mcu.Pin27=PD11
Mcu.Pin28=PD12
Mcu.Pin29=PD13
Mcu.Pin3=PF1
Mcu.Pin30=PD14
Mcu.Pin31=PD15
Mcu.Pin32=PG2
Mcu.Pin33=PG3
Mcu.Pin34=PG4
Mcu.Pin35=PG5
Mcu.Pin36=PG6
Mcu.Pin37=PG7
Mcu.Pin38=PG8
Mcu.Pin39=PC6
Mcu.Pin4=PF2
Mcu.Pin40=PA13
Mcu.Pin41=PA14
Mcu.Pin42=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin43=VP_SYS_VS_Systick
Mcu.Pin44=VP_TIM4_VS_ClockSourceINT
Mcu.Pin5=PF3
Mcu.Pin6=PF4
Mcu.Pin7=PF5
Mcu.Pin8=PF6
Mcu.Pin9=PF7
Mcu.PinsNb=45
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407ZETx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA2_Stream0_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA1.Signal=ADCx_IN1
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Signal=ADCx_IN2
PA3.Signal=ADCx_IN3
PB0.Signal=ADCx_IN8
PB1.Signal=ADCx_IN9
PC0.Signal=ADCx_IN10
PC1.Signal=ADCx_IN11
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC2.Signal=ADCx_IN12
PC5.Signal=ADCx_IN15
PC6.Locked=true
PC6.Signal=GPIO_Output
PD10.Locked=true
PD10.Signal=GPIO_Output
PD11.Locked=true
PD11.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.Locked=true
PD13.Signal=GPIO_Output
PD14.Locked=true
PD14.Signal=GPIO_Output
PD15.Locked=true
PD15.Signal=GPIO_Output
PD8.Locked=true
PD8.Signal=GPIO_Output
PD9.Locked=true
PD9.Signal=GPIO_Output
PF0.Locked=true
PF0.Signal=GPIO_Input
PF1.Locked=true
PF1.Signal=GPIO_Input
PF10.Locked=true
PF10.Signal=GPIO_Input
PF2.Locked=true
PF2.Signal=GPIO_Input
PF3.Locked=true
PF3.Signal=GPIO_Input
PF4.Locked=true
PF4.Signal=GPIO_Input
PF5.Locked=true
PF5.Signal=GPIO_Input
PF6.Locked=true
PF6.Signal=GPIO_Input
PF7.Locked=true
PF7.Signal=GPIO_Input
PF8.Locked=true
PF8.Signal=GPIO_Input
PF9.Locked=true
PF9.Signal=GPIO_Input
PG2.Locked=true
PG2.Signal=GPIO_Output
PG3.Locked=true
PG3.Signal=GPIO_Output
PG4.Locked=true
PG4.Signal=GPIO_Output
PG5.Locked=true
PG5.Signal=GPIO_Output
PG6.Locked=true
PG6.Signal=GPIO_Output
PG7.Locked=true
PG7.Signal=GPIO_Output
PG8.Locked=true
PG8.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=STM32_Project.ioc
ProjectManager.ProjectName=STM32_Project
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_ADC1_Init-ADC1-false-HAL-true,5-MX_TIM4_Init-TIM4-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=25
RCC.PLLN=336
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=96000000
SH.ADCx_IN1.0=ADC1_IN1,IN1
SH.ADCx_IN1.ConfNb=1
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SH.ADCx_IN11.0=ADC1_IN11,IN11
SH.ADCx_IN11.ConfNb=1
SH.ADCx_IN12.0=ADC1_IN12,IN12
SH.ADCx_IN12.ConfNb=1
SH.ADCx_IN15.0=ADC1_IN15,IN15
SH.ADCx_IN15.ConfNb=1
SH.ADCx_IN2.0=ADC1_IN2,IN2
SH.ADCx_IN2.ConfNb=1
SH.ADCx_IN3.0=ADC1_IN3,IN3
SH.ADCx_IN3.ConfNb=1
SH.ADCx_IN8.0=ADC1_IN8,IN8
SH.ADCx_IN8.ConfNb=1
SH.ADCx_IN9.0=ADC1_IN9,IN9
SH.ADCx_IN9.ConfNb=1
TIM4.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM4.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterSlaveMode,TIM_MasterOutputTrigger
TIM4.Period=624
TIM4.Prescaler=41
TIM4.TIM_MasterOutputTrigger=TIM_TRGO_OC1
TIM4.TIM_MasterSlaveMode=TIM_MASTERSLAVEMODE_ENABLE
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
