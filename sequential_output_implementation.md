# STM32F4 Sequential Output Implementation

## Implementation Summary

This implementation creates a 1-second sequential output pattern for the PG2-PG8 output pins in the STM32F407ZET6 microcontroller project.

## Output Pins Configuration

The project uses the following output pins:

### Port G (7 pins)  
- PG2, PG3, PG4, PG5, PG6, PG7, PG8

## Implementation Details

The sequential output pattern is implemented in the FreeRTOS default task (`StartDefaultTask` function in `freertos.c`):

1. **Pin Arrays**: Two arrays define the output pins and their corresponding ports:
   - `outputPins[]`: Contains the pin numbers in the desired sequence
   - `outputPorts[]`: Contains the port pointers for each pin

2. **Sequence Logic**: 
   - Each pin is turned ON (GPIO_PIN_SET) for 1 second
   - After 1 second, the pin is turned OFF (GPIO_PIN_RESET)
   - The sequence moves to the next pin
   - After all 7 pins (PG2-PG8) have been processed, there's a 100ms delay before restarting

3. **Timing**: Uses FreeRTOS `osDelay(1000)` for accurate 1-second delays

## Code Location

- Main implementation: `Core/Src/freertos.c` (lines 119-158)
- GPIO configuration: `Core/Src/gpio.c`
- Pin definitions: Referenced from `gpio.h`

## Expected Behavior

When the program runs:
1. PG2 turns ON for 1 second, then OFF
2. PG3 turns ON for 1 second, then OFF
3. ... (continues through PG8)
4. After PG8 turns OFF, there's a 100ms pause
5. The sequence repeats from PG2

This creates a "wave" or "chasing" effect across all output pins with each pin active for exactly 1 second.

## Build Instructions

To build and run this project:
1. Open the project in STM32CubeIDE
2. Build the project (Project -> Build Project)
3. Flash to the STM32F407ZET6 microcontroller
4. The sequential output pattern will start automatically

## Verification

The implementation can be verified by:
1. Connecting LEDs to the output pins (with appropriate current-limiting resistors)
2. Using an oscilloscope or logic analyzer to monitor the pin states
3. Observing the 1-second ON / 1-second OFF pattern for each pin in sequence