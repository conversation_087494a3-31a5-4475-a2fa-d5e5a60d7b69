{"name": "STM32_Project", "type": "ARM", "dependenceList": [], "srcDirs": ["Core", "Middlewares", "Drivers"], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "1af499ae68e16ace1992ea580c7739c5"}, "targets": {"Debug": {"excludeList": [], "toolchain": "GCC", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": true, "storageLayout": {"RAM": [], "ROM": []}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "ST", "cpuName": "STM32F407ZE"}, "proType": 1, "speed": 2000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["Core/Inc", "Drivers/STM32F4xx_HAL_Driver/Inc", "Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "Middlewares/Third_Party/FreeRTOS/Source/include", "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2", "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F", "Drivers/CMSIS/Device/ST/STM32F4xx/Include", "Drivers/CMSIS/Include"], "libList": [], "defineList": ["DEBUG", "USE_HAL_DRIVER", "STM32F407xx"]}, "builderOptions": {"GCC": {"version": 5, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"$float-abi-type": "hard", "output-debug-info": "enable", "misc-control": []}, "c/cpp-compiler": {"language-c": "c11", "language-cpp": "c++11", "optimization": "level-debug", "warnings": "all-warnings", "one-elf-section-per-function": true, "one-elf-section-per-data": true, "C_FLAGS": "", "CXX_FLAGS": ""}, "asm-compiler": {"ASM_FLAGS": "-DDEBUG"}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "remove-unused-input-sections": true, "LD_FLAGS": "-TSTM32F407ZETX_FLASH.ld", "LIB_FLAGS": ""}}}}, "Release": {"excludeList": [], "toolchain": "GCC", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": true, "storageLayout": {"RAM": [], "ROM": []}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "ST", "cpuName": "STM32F407ZE"}, "proType": 1, "speed": 2000, "otherCmds": ""}, "uploadConfigMap": {}, "builderOptions": {"GCC": {"version": 5, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"$float-abi-type": "hard", "output-debug-info": "enable", "misc-control": []}, "c/cpp-compiler": {"language-c": "c11", "language-cpp": "c++11", "optimization": "level-debug", "warnings": "all-warnings", "one-elf-section-per-function": true, "one-elf-section-per-data": true, "C_FLAGS": "", "CXX_FLAGS": ""}, "asm-compiler": {"ASM_FLAGS": ""}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "remove-unused-input-sections": true, "LD_FLAGS": "-TSTM32F407ZETX_FLASH.ld", "LIB_FLAGS": ""}}}, "custom_dep": {"name": "default", "incList": ["Core/Inc", "Drivers/STM32F4xx_HAL_Driver/Inc", "Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "Middlewares/Third_Party/FreeRTOS/Source/include", "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2", "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F", "Drivers/CMSIS/Device/ST/STM32F4xx/Include", "Drivers/CMSIS/Include"], "defineList": ["USE_HAL_DRIVER", "STM32F407xx"], "libList": []}}}, "version": "3.6"}