{"permissions": {"allow": ["Bash(mv \"D:\\TEXT\\D.code-workspace\" \"D:\\TEXT\\STM32_Project.code-workspace\")", "Bash(mv \"D:\\TEXT\\D.ioc\" \"D:\\TEXT\\STM32_Project.ioc\")", "<PERSON><PERSON>(echo:*)", "Bash(find:*)", "Bash(make clean)", "<PERSON><PERSON>(dir:*)", "Bash(rm:*)", "Bash(\"D:\\TEXT\\unify_builder.cmd\" -p \"D:\\TEXT\\build\\Debug\\builder.params\")", "<PERSON><PERSON>(code:*)", "Ba<PERSON>(arm-none-eabi-gcc:*)", "Bash(arm-none-eabi-size:*)", "Bash(grep:*)", "Ba<PERSON>(cmd:*)", "Bash(where:*)", "Bash(unify_builder:*)"], "deny": []}}