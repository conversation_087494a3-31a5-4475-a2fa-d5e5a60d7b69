/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/

/* USER CODE BEGIN Private defines */
/* LED流水灯控制宏定义 */
#define LED_DELAY_MS         200    // 流水灯延时时间(ms)
#define LED_PD15_PIN        GPIO_PIN_15
#define LED_PG2_PIN         GPIO_PIN_2
#define LED_PG3_PIN         GPIO_PIN_3
#define LED_PG4_PIN         GPIO_PIN_4
#define LED_PG5_PIN         GPIO_PIN_5
#define LED_PG6_PIN         GPIO_PIN_6
#define LED_PG7_PIN         GPIO_PIN_7
#define LED_PG8_PIN         GPIO_PIN_8
#define LED_PC6_PIN         GPIO_PIN_6
#define LED_PG_ALL_PINS     (LED_PG2_PIN | LED_PG3_PIN | LED_PG4_PIN | \
                             LED_PG5_PIN | LED_PG6_PIN | LED_PG7_PIN | LED_PG8_PIN)
#define LED_PC_ALL_PINS     (LED_PC6_PIN)
#define LED_PD_ALL_PINS     (LED_PD15_PIN)
#define LED_ALL_PINS        (LED_PD15_PIN | LED_PG_ALL_PINS | LED_PC6_PIN)
#define LED_COUNT           9       // PD15 + PG2-PG8 + PC6共9个LED

/* 优化的LED批量操作宏 */
#define LED_BATCH_WRITE(port, pins, state) HAL_GPIO_WritePin(port, pins, state)
#define LED_ALL_OFF() { \
  HAL_GPIO_WritePin(GPIOD, LED_PD15_PIN, GPIO_PIN_RESET); \
  HAL_GPIO_WritePin(GPIOG, LED_PG_ALL_PINS, GPIO_PIN_RESET); \
  HAL_GPIO_WritePin(GPIOC, LED_PC6_PIN, GPIO_PIN_RESET); \
}
#define LED_ALL_ON() { \
  HAL_GPIO_WritePin(GPIOD, LED_PD15_PIN, GPIO_PIN_SET); \
  HAL_GPIO_WritePin(GPIOG, LED_PG_ALL_PINS, GPIO_PIN_SET); \
  HAL_GPIO_WritePin(GPIOC, LED_PC6_PIN, GPIO_PIN_SET); \
}

/* 深度优化：端口级批量操作宏 - 直接操作BSRR寄存器 */
#define LED_PORT_WRITE(port, mask, state) do { \
  if (state) { \
    (port)->BSRR = (mask); \
  } else { \
    (port)->BSRR = ((mask) << 16); \
  } \
} while(0)

#define LED_PD_ALL_MASK  (LED_PD15_PIN)
#define LED_PG_ALL_MASK  (LED_PG_ALL_PINS)
#define LED_PC_ALL_MASK  (LED_PC6_PIN)
#define LED_ALL_MASK     (LED_PD_ALL_MASK | LED_PG_ALL_MASK | LED_PC_ALL_MASK)

/* 超快速批量操作 */
#define LED_ALL_OFF_FAST() { \
  LED_PORT_WRITE(GPIOD, LED_PD_ALL_MASK, 0); \
  LED_PORT_WRITE(GPIOG, LED_PG_ALL_MASK, 0); \
  LED_PORT_WRITE(GPIOC, LED_PC_ALL_MASK, 0); \
}
#define LED_ALL_ON_FAST() { \
  LED_PORT_WRITE(GPIOD, LED_PD_ALL_MASK, 1); \
  LED_PORT_WRITE(GPIOG, LED_PG_ALL_MASK, 1); \
  LED_PORT_WRITE(GPIOC, LED_PC_ALL_MASK, 1); \
}

/* 按键控制宏定义 */
#define BUTTON_MODE_PIN     GPIO_PIN_0    // PF0作为模式切换按键
#define BUTTON_SPEED_PIN    GPIO_PIN_1    // PF1作为速度调节按键
#define BUTTON_PORT         GPIOF

/* 速度级别定义 */
typedef enum {
  SPEED_SLOW = 500,    // 慢速：500ms
  SPEED_NORMAL = 150, // 正常：150ms
  SPEED_FAST = 80,    // 快速：80ms
  SPEED_VERY_FAST = 40, // 极快：40ms
  SPEED_COUNT         // 速度级别总数
} LedSpeed;

/* 流水灯模式枚举 */
typedef enum {
  LED_MODE_SINGLE,        // 单灯流水
  LED_MODE_CHASE,         // 追逐效果
  LED_MODE_ACCUMULATE,    // 累积效果
  LED_MODE_BREATH,        // 呼吸效果
  LED_MODE_RAINBOW,       // 彩虹效果
  LED_MODE_BLINK,         // 闪烁效果
  LED_MODE_WAVE,          // 波浪效果
  LED_MODE_RANDOM,        // 随机效果
  LED_MODE_ALTERNATE,     // 交替效果
  LED_MODE_SNAKE,         // S型蛇形流动
  LED_MODE_EXPLODE,       // 爆炸扩散效果
  LED_MODE_BIDIRECTIONAL, // 双向追逐
  LED_MODE_STAIRCASE,     // 阶梯递进
  LED_MODE_COUNT          // 模式总数
} LedMode;

/* LED状态结构体 */
typedef struct {
  uint16_t pins[LED_COUNT];
  GPIO_TypeDef *ports[LED_COUNT];
  uint8_t current_pos;
  uint8_t direction;
  LedMode mode;
  uint16_t pattern;
  uint8_t breath_dir;
  uint32_t random_seed;
  uint8_t wave_phase;
  uint8_t alternate_state;
  uint8_t speed_level;
  uint32_t current_delay;
} LedController;
/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
