# 【强制执行】FreeRTOS + STM32CubeIDE 开发规范 V1.1

## 技术栈声明

**开发环境**：STM32CubeIDE + HAL库 + FreeRTOS
**目标平台**：STM32系列单片机
**操作系统**：FreeRTOS实时操作系统
**开发语言**：C/C++

【强制执行】每次对话开始时，必须完整阅读并理解本指南的所有内容，严格按照其中的理念、流程和技术标准执行任务。

**重要提醒**：
- 本文件为强制执行文件
- 每次开始新任务前必须完整阅读
- 不得跳过任何章节或步骤
- 必须严格按照指南执行所有开发工作
- 违反本指南将导致开发质量下降

## 理念

### 核心信念

- **渐进式开发而非一次性完成** - 小步修改，确保编译和测试通过
- **学习现有代码** - 实现前先研究和规划
- **实用主义而非教条主义** - 适应项目实际情况
- **清晰的意图而非花哨的代码** - 代码要简单明了

### FreeRTOS + HAL库特色

- **HAL库优先** - 优先使用HAL库API，避免直接寄存器操作
- **RTOS思维** - 任务化、异步化、事件驱动编程
- **资源管理** - 合理使用任务、队列、信号量、互斥锁
- **实时性保障** - 确保关键任务的实时响应

### 简洁意味着

- 每个任务/函数单一职责
- 避免过早抽象和过度优化
- 不用花哨技巧 - 选择最稳定的HAL库方案
- 如果需要解释，那就太复杂了

## 流程

### 1. 规划分阶段

将复杂工作分解为3-5个阶段。在`IMPLEMENTATION_PLAN.md`中记录：

```markdown
## 第N阶段：[名称]
**目标**：[具体交付物]
**成功标准**：[可测试的结果]
**测试**：[具体测试用例]
**状态**：[未开始|进行中|已完成]
```
- 进展时更新状态
- 所有阶段完成后删除文件

### 2. 代码开发流程

1. **理解** - 研究代码库中的现有HAL库模式和FreeRTOS任务结构
2. **测试** - 先写测试（红色）
3. **实现** - 使用HAL库API实现功能（绿色）
4. **重构** - 在测试通过的前提下清理代码
5. **提交** - 清晰的提交信息链接到计划

### 3. 卡住时（3次尝试后）

**关键**：每个问题最多尝试3次，然后停止。

1. **记录失败原因**：
   - 尝试了什么HAL库函数
   - 具体错误信息和HAL库返回值
   - 认为失败的原因

2. **研究替代方案**：
   - 查找HAL库文档和示例
   - 研究FreeRTOS API使用方法
   - 找到2-3个类似实现

3. **质疑基础**：
   - 这是否是正确的HAL库使用方式？
   - FreeRTOS任务划分是否合理？
   - 能否拆分为更小的问题？

4. **尝试不同角度**：
   - 不同的HAL库函数组合？
   - 不同的FreeRTOS同步机制？
   - 不同的任务优先级配置？

## 技术标准

### HAL库使用规范

- **HAL库初始化** - 基于现有初始化代码模式使用HAL库API
- **错误处理** - 检查所有HAL库函数的返回值
- **回调函数** - 合理使用HAL库回调机制
- **中断处理** - 遵循HAL库中断处理规范
- **DMA使用** - 优先使用DMA提高效率

### HAL库错误处理（补充示例）
```c
HAL_StatusTypeDef status = HAL_UART_Transmit(&huart2, data, len, 100);
if(status != HAL_OK) {
    // 必须记录错误源
    log_error(UART_ERR | status); 
    // 触发错误恢复任务
    xTaskNotify(Recovery_Task, ERR_UART, eSetBits);
}
```

### FreeRTOS任务管理

- **任务创建** - 合理设置任务优先级和栈大小
- **任务通信** - 使用队列、信号量、事件组进行通信
- **资源同步** - 使用互斥锁保护共享资源
- **内存管理** - 使用FreeRTOS内存管理API
- **任务调度** - 避免任务阻塞时间过长

### 架构原则

- **模块化设计** - 按功能模块划分任务和文件
- **事件驱动** - 使用FreeRTOS事件机制进行任务调度
- **分层架构** - HAL层、驱动层、应用层清晰分离
- **错误隔离** - 任务间错误隔离，避免级联故障

### 代码质量

- **每次提交必须**：
  - 成功编译（STM32CubeIDE无错误警告）
  - 通过所有现有测试
  - 包含新功能的测试
  - 遵循HAL库命名规范和项目格式化/代码规范

- **提交前**：
  - 运行STM32CubeIDE代码分析工具
  - 检查HAL库函数使用是否正确
  - 自我审查FreeRTOS任务配置
  - 确保提交信息解释"为什么"

### 错误处理

- HAL库错误处理：检查HAL_StatusTypeDef返回值
- FreeRTOS错误处理：检查API返回值和队列状态
- 任务错误处理：实现任务级错误恢复机制
- 系统错误处理：实现看门狗和系统复位机制

## 决策框架

当存在多个有效方法时，基于以下选择：

1. **HAL库兼容性** - 是否符合HAL库设计理念？
2. **FreeRTOS友好性** - 是否适合RTOS环境？
3. **可测试性** - 能否轻松测试？
4. **可读性** - 6个月后别人能否理解？
5. **一致性** - 是否符合项目HAL库使用模式？
6. **简单性** - 这是最简单的可行方案吗？
7. **可逆性** - 后续更改难度如何？

## 项目集成

### 学习代码库

- 找到3个相似功能/组件的HAL库实现
- 识别FreeRTOS任务结构和通信模式
- 使用相同的HAL库配置和FreeRTOS设置
- 遵循现有的错误处理模式

### 开发环境

- **STM32CubeIDE** - 主要开发IDE，代码编辑、编译、调试
- **HAL库** - 硬件抽象层库API
- **FreeRTOS** - 实时操作系统内核
- **调试工具** - 串口输出、调试信息

## 质量门禁

### 完成定义

- [ ] 测试已编写并通过
- [ ] 代码遵循HAL库命名规范
- [ ] FreeRTOS任务配置合理
- [ ] 0 warning & 0 error编译
- [ ] 栈水位检测通过（FreeRTOS堆栈检查）
- [ ] 关键路径执行时间测量（<10us）
- [ ] HAL库函数使用正确
- [ ] 提交信息清晰
- [ ] 实现符合计划
- [ ] 无任务号待办事项

### 测试指南

- **单元测试** - 测试HAL库驱动和业务逻辑
- **集成测试** - 测试FreeRTOS任务间通信
- **系统测试** - 测试整体系统功能
- **压力测试** - 测试系统在高负载下的稳定性
- **实时性测试** - 测试关键任务的响应时间


## 重要提醒

**永不**：
- 忽略HAL库函数的返回值检查
- 在FreeRTOS任务中使用阻塞式延时
- 滥用全局变量破坏任务隔离
- 使用`--no-verify`绕过提交钩子

**始终**：
- 检查所有HAL库函数的返回值
- 合理配置FreeRTOS任务优先级和栈大小
- 使用FreeRTOS同步机制保护共享资源
- 遵循HAL库和FreeRTOS的最佳实践
- 增量提交工作代码
- 更新计划文档
- 从现有实现中学习
- 3次失败后停止并重新评估


## 强化执行条款

⚠️ **每次代码提交前必须验证**：
1. 已完成`质量门禁`清单所有检查项
2. 在CubeIDE中执行`Clean & Build`无报错