/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "gpio.h"
#include <stdint.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
/* Definitions for defaultTask */
osThreadId_t defaultTaskHandle;
/* Definitions for ledTask */
osThreadId_t ledTaskHandle;

const osThreadAttr_t defaultTask_attributes = {
  .name = "defaultTask",
  .stack_size = 64 * 4,  // 优化：减少栈大小，简单延时任务不需要512字节
  .priority = (osPriority_t) osPriorityBelowNormal,  // 优化：降低优先级
};

const osThreadAttr_t ledTask_attributes = {
  .name = "ledTask",
  .stack_size = 256 * 4,  // 优化：LED控制任务需要更多栈空间
  .priority = (osPriority_t) osPriorityNormal,  // 保持正常优先级
};

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
static void led_mode_single(LedController *ctrl);
static void led_mode_chase(LedController *ctrl);
static void led_mode_accumulate(LedController *ctrl);
static void led_mode_breath(LedController *ctrl);
static void led_mode_rainbow(LedController *ctrl);
static void led_mode_blink(LedController *ctrl);
static void led_mode_wave(LedController *ctrl);
static void led_mode_random(LedController *ctrl);
static void led_mode_alternate(LedController *ctrl);
static void led_mode_snake(LedController *ctrl);
static void led_mode_explode(LedController *ctrl);
static void led_mode_bidirectional(LedController *ctrl);
static void led_mode_staircase(LedController *ctrl);
static uint32_t simple_random(uint32_t *seed);
static void led_batch_set_by_pattern(LedController *ctrl, uint16_t pattern);
static uint8_t is_button_pressed(uint16_t pin);
/* USER CODE END FunctionPrototypes */

void StartDefaultTask(void *argument);
void StartLedTask(void *argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* creation of defaultTask */
  defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* creation of ledTask */
  ledTaskHandle = osThreadNew(StartLedTask, NULL, &ledTask_attributes);
  /* USER CODE END RTOS_THREADS */

  /* USER CODE BEGIN RTOS_EVENTS */
  /* add events, ... */
  /* USER CODE END RTOS_EVENTS */

}

/* USER CODE BEGIN Header_StartDefaultTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void *argument)
{

  
  for(;;)
  {
    osDelay(1000); // 空循环
  }
  /* USER CODE END StartDefaultTask */
}

/* USER CODE BEGIN Header_StartLedTask */
/**
  * @brief  Function implementing the ledTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartLedTask */
void StartLedTask(void *argument)
{
  /* LED控制器初始化 - PD15作为第一个LED，PC6作为最后一个LED */
  LedController led_ctrl = {
    .pins = {LED_PD15_PIN, LED_PG2_PIN, LED_PG3_PIN, LED_PG4_PIN,
             LED_PG5_PIN, LED_PG6_PIN, LED_PG7_PIN, LED_PG8_PIN, LED_PC6_PIN},
    .ports = {GPIOD, GPIOG, GPIOG, GPIOG, GPIOG, GPIOG, GPIOG, GPIOG, GPIOC},
    .current_pos = 0,
    .direction = 1,
    .mode = LED_MODE_SINGLE,
    .pattern = 0,
    .breath_dir = 1,
    .random_seed = 123456789,
    .wave_phase = 0,
    .alternate_state = 0,
    .speed_level = 1,  // 默认正常速度
    .current_delay = SPEED_NORMAL
  };
  
  uint32_t mode_timer = 0;
  const uint32_t MODE_SWITCH_INTERVAL = 5000; // 5秒切换模式
  
  /* 初始化所有LED为熄灭状态 */
  LED_ALL_OFF_FAST();
  
  for(;;)
  {
    /* 检查按键输入 - 使用优化后的按键检测函数 */
    if (is_button_pressed(BUTTON_MODE_PIN)) {
      /* 模式切换按键按下 */
      led_ctrl.mode = (led_ctrl.mode + 1) % LED_MODE_COUNT;
      led_ctrl.current_pos = 0;
      led_ctrl.direction = 1;
      /* 重置所有LED */
      LED_ALL_OFF_FAST();
      /* 等待按键释放 */
      while(HAL_GPIO_ReadPin(BUTTON_PORT, BUTTON_MODE_PIN) == GPIO_PIN_SET);
    }
    
    if (is_button_pressed(BUTTON_SPEED_PIN)) {
      /* 速度调节按键按下 */
      /* 切换速度级别 */
      led_ctrl.speed_level = (led_ctrl.speed_level + 1) % SPEED_COUNT;
        switch(led_ctrl.speed_level) {
          case 0:
            led_ctrl.current_delay = SPEED_SLOW;
            break;
          case 1:
            led_ctrl.current_delay = SPEED_NORMAL;
            break;
          case 2:
            led_ctrl.current_delay = SPEED_FAST;
            break;
          case 3:
            led_ctrl.current_delay = SPEED_VERY_FAST;
            break;
          default:
            led_ctrl.current_delay = SPEED_NORMAL;
            break;
        }
        /* 等待按键释放 */
        while(HAL_GPIO_ReadPin(BUTTON_PORT, BUTTON_SPEED_PIN) == GPIO_PIN_SET);
      }
    }
    
    /* 根据当前模式执行对应的LED效果 */
    switch(led_ctrl.mode) {
      case LED_MODE_SINGLE:
        led_mode_single(&led_ctrl);
        break;
      case LED_MODE_CHASE:
        led_mode_chase(&led_ctrl);
        break;
      case LED_MODE_ACCUMULATE:
        led_mode_accumulate(&led_ctrl);
        break;
      case LED_MODE_BREATH:
        led_mode_breath(&led_ctrl);
        break;
      case LED_MODE_RAINBOW:
        led_mode_rainbow(&led_ctrl);
        break;
      case LED_MODE_BLINK:
        led_mode_blink(&led_ctrl);
        break;
      case LED_MODE_WAVE:
        led_mode_wave(&led_ctrl);
        break;
      case LED_MODE_RANDOM:
        led_mode_random(&led_ctrl);
        break;
      case LED_MODE_ALTERNATE:
        led_mode_alternate(&led_ctrl);
        break;
      case LED_MODE_SNAKE:
        led_mode_snake(&led_ctrl);
        break;
      case LED_MODE_EXPLODE:
        led_mode_explode(&led_ctrl);
        break;
      case LED_MODE_BIDIRECTIONAL:
        led_mode_bidirectional(&led_ctrl);
        break;
      case LED_MODE_STAIRCASE:
        led_mode_staircase(&led_ctrl);
        break;
      default:
        led_ctrl.mode = LED_MODE_SINGLE;
        break;
    }
    
    /* 模式自动切换 */
    mode_timer += led_ctrl.current_delay;
    if (mode_timer >= MODE_SWITCH_INTERVAL) {
      mode_timer = 0;
      led_ctrl.mode = (led_ctrl.mode + 1) % LED_MODE_COUNT;
      led_ctrl.current_pos = 0;
      led_ctrl.direction = 1;
      LED_ALL_OFF_FAST();
    }
    
    osDelay(led_ctrl.current_delay);
  }
  /* USER CODE END StartLedTask */
}

/* 批量LED设置辅助函数 - 深度优化 */
static void led_batch_set_by_pattern(LedController *ctrl, uint16_t pattern)
{
  uint16_t pd_mask = 0, pg_mask = 0, pc_mask = 0;
  
  /* 按端口分组计算掩码 */
  for (uint8_t i = 0; i < LED_COUNT; i++) {
    if (pattern & (1 << i)) {
      if (ctrl->ports[i] == GPIOD) pd_mask |= ctrl->pins[i];
      else if (ctrl->ports[i] == GPIOG) pg_mask |= ctrl->pins[i];
      else if (ctrl->ports[i] == GPIOC) pc_mask |= ctrl->pins[i];
    }
  }
  
  /* 先全部熄灭，再按端口点亮 */
  LED_ALL_OFF_FAST();
  if (pd_mask) LED_PORT_WRITE(GPIOD, pd_mask, 1);
  if (pg_mask) LED_PORT_WRITE(GPIOG, pg_mask, 1);
  if (pc_mask) LED_PORT_WRITE(GPIOC, pc_mask, 1);
}

/* 按键检测辅助函数 - 消抖处理 */
static uint8_t is_button_pressed(uint16_t pin)
{
  if (HAL_GPIO_ReadPin(BUTTON_PORT, pin) == GPIO_PIN_SET) {
    osDelay(50); // 消抖
    if (HAL_GPIO_ReadPin(BUTTON_PORT, pin) == GPIO_PIN_SET) {
      return 1; // 按键确实按下
    }
  }
  return 0; // 按键未按下
}

/* 单灯流水模式 - 深度优化版本 */
static void led_mode_single(LedController *ctrl)
{
  /* 熄灭所有LED - 使用超快速操作 */
  LED_ALL_OFF_FAST();
  
  /* 点亮当前LED - 使用直接寄存器操作 */
  LED_PORT_WRITE(ctrl->ports[ctrl->current_pos], ctrl->pins[ctrl->current_pos], 1);
  
  /* 更新位置 - 优化边界检查 */
  if (ctrl->direction) {
    ctrl->current_pos++;
    if (ctrl->current_pos >= LED_COUNT - 1) {
      ctrl->direction = 0;
    }
  } else {
    ctrl->current_pos--;
    if (ctrl->current_pos <= 0) {
      ctrl->direction = 1;
    }
  }
}

/* 追逐效果模式 - 深度优化版本 */
static void led_mode_chase(LedController *ctrl)
{
  uint16_t pattern = 0;
  
  /* 创建追逐模式：两个LED相邻移动 */
  pattern |= (1 << ctrl->current_pos);
  pattern |= (1 << ((ctrl->current_pos + 1) % LED_COUNT));
  
  /* 使用辅助函数批量设置 */
  led_batch_set_by_pattern(ctrl, pattern);
  
  ctrl->current_pos = (ctrl->current_pos + 1) % LED_COUNT;
}

/* 累积效果模式 - 深度优化版本 */
static void led_mode_accumulate(LedController *ctrl)
{
  uint16_t pattern = 0;
  
  /* 创建累积模式：点亮从开始到当前位置的所有LED */
  for (uint8_t i = 0; i <= ctrl->current_pos; i++) {
    pattern |= (1 << i);
  }
  
  /* 使用辅助函数批量设置 */
  led_batch_set_by_pattern(ctrl, pattern);
  
  /* 更新位置 */
  if (ctrl->direction) {
    ctrl->current_pos++;
    if (ctrl->current_pos >= LED_COUNT - 1) {
      ctrl->direction = 0;
    }
  } else {
    if (ctrl->current_pos > 0) {
      ctrl->current_pos--;
    } else {
      ctrl->direction = 1;
    }
  }
}

/* 呼吸效果模式 - 优化版本 */
static void led_mode_breath(LedController *ctrl)
{
  /* 简单的呼吸效果：所有LED同时闪烁 */
  static uint8_t breath_counter = 0;
  
  if (ctrl->breath_dir) {
    breath_counter++;
    if (breath_counter >= 10) {
      ctrl->breath_dir = 0;
    }
  } else {
    breath_counter--;
    if (breath_counter == 0) {
      ctrl->breath_dir = 1;
    }
  }
  
  /* 根据呼吸计数器设置LED状态 - 优化为批量操作 */
  if (breath_counter > 5) {
    LED_ALL_ON_FAST();
  } else {
    LED_ALL_OFF_FAST();
  }
}

/* 彩虹效果模式 - 多种颜色组合循环 */
static void led_mode_rainbow(LedController *ctrl)
{
  static uint8_t rainbow_step = 0;
  
  /* 创建彩虹效果模式 */
  uint16_t patterns[] = {
    0x0001,  // 单个LED
    0x0003,  // 两个相邻LED
    0x0007,  // 三个相邻LED
    0x000F,  // 四个相邻LED
    0x001F,  // 五个相邻LED
    0x003F,  // 六个相邻LED
    0x007F,  // 七个相邻LED
    0x00FF,  // 八个相邻LED
    0x01FF,  // 全部LED
    0x00AA,  // 间隔LED
    0x0055,  // 间隔LED(错位)
    0x0180,  // 两端LED
    0x0140,  // 中心对称
    0x0000   // 全灭
  };
  
  uint16_t current_pattern = patterns[rainbow_step % (sizeof(patterns)/sizeof(patterns[0]))];
  
  /* 使用辅助函数应用图案 */
  led_batch_set_by_pattern(ctrl, current_pattern);
  
  rainbow_step++;
  if (rainbow_step >= sizeof(patterns)/sizeof(patterns[0])) {
    rainbow_step = 0;
  }
}

/* 闪烁效果模式 - 全部LED同时闪烁 - 优化版本 */
static void led_mode_blink(LedController *ctrl)
{
  static uint8_t blink_counter = 0;
  
  blink_counter++;
  
  /* 闪烁模式：亮2个周期，灭2个周期 - 优化为批量操作 */
  if ((blink_counter / 2) % 2 == 0) {
    LED_ALL_ON_FAST();
  } else {
    LED_ALL_OFF_FAST();
  }
}

/* 波浪效果模式 - 从中心向两端扩散 */
static void led_mode_wave(LedController *ctrl)
{
  ctrl->wave_phase++;
  
  /* 从中心向两端扩散的波浪效果 */
  uint8_t center = LED_COUNT / 2;
  uint8_t radius = (ctrl->wave_phase / 2) % (center + 1);
  
  for (uint8_t i = 0; i < LED_COUNT; i++) {
    uint8_t distance = (i > center) ? (i - center) : (center - i);
    if (distance <= radius) {
      HAL_GPIO_WritePin(ctrl->ports[i], ctrl->pins[i], GPIO_PIN_SET);
    } else {
      HAL_GPIO_WritePin(ctrl->ports[i], ctrl->pins[i], GPIO_PIN_RESET);
    }
  }
}

/* 随机效果模式 - 随机点亮LED - 优化版本 */
static void led_mode_random(LedController *ctrl)
{
  uint16_t pattern = 0;
  
  /* 随机选择LED点亮 */
  for (uint8_t i = 0; i < LED_COUNT; i++) {
    if (simple_random(&ctrl->random_seed) % 3 == 0) {
      pattern |= (1 << i);
    }
  }
  
  /* 使用辅助函数批量设置 */
  led_batch_set_by_pattern(ctrl, pattern);
}

/* 交替效果模式 - 奇偶位置LED交替闪烁 - 优化版本 */
static void led_mode_alternate(LedController *ctrl)
{
  uint16_t pattern = 0;
  
  ctrl->alternate_state = !ctrl->alternate_state;
  
  /* 创建交替模式 */
  for (uint8_t i = 0; i < LED_COUNT; i++) {
    if ((i % 2) == ctrl->alternate_state) {
      pattern |= (1 << i);
    }
  }
  
  /* 使用辅助函数批量设置 */
  led_batch_set_by_pattern(ctrl, pattern);
}

/* S型蛇形流动模式 - 优化版本 */
static void led_mode_snake(LedController *ctrl)
{
  /* 熄灭所有LED */
  LED_ALL_OFF_FAST();
  
  /* S型路径：0->1->2->3->4->5->6->7->8->7->6->5->4->3->2->1 */
  static uint8_t snake_path[] = {0,1,2,3,4,5,6,7,8,7,6,5,4,3,2,1};
  static uint8_t path_index = 0;
  
  /* 点亮当前位置的LED */
  uint8_t current_led = snake_path[path_index];
  LED_PORT_WRITE(ctrl->ports[current_led], ctrl->pins[current_led], 1);
  
  /* 更新路径索引 */
  path_index = (path_index + 1) % (sizeof(snake_path)/sizeof(snake_path[0]));
}

/* 爆炸扩散效果模式 - 优化版本 */
static void led_mode_explode(LedController *ctrl)
{
  static uint8_t explode_phase = 0;
  
  /* 熄灭所有LED */
  LED_ALL_OFF_FAST();
  
  /* 定义爆炸模式：从中心向外扩散 */
  uint8_t center = LED_COUNT / 2;
  uint8_t radius = explode_phase % (center + 1);
  
  /* 点亮在爆炸半径内的LED - 使用模式优化 */
  uint16_t pattern = 0;
  for (uint8_t i = 0; i < LED_COUNT; i++) {
    uint8_t distance = (i > center) ? (i - center) : (center - i);
    if (distance <= radius) {
      pattern |= (1 << i);
    }
  }
  led_batch_set_by_pattern(ctrl, pattern);
  
  /* 更新爆炸相位 */
  explode_phase++;
  if (explode_phase > center * 2) {
    explode_phase = 0;
  }
}

/* 双向追逐模式 - 优化版本 */
static void led_mode_bidirectional(LedController *ctrl)
{
  static uint8_t left_pos = 0;
  static uint8_t right_pos = LED_COUNT - 1;
  
  /* 熄灭所有LED */
  LED_ALL_OFF_FAST();
  
  /* 点亮左右两个追逐的LED - 使用快速操作 */
  LED_PORT_WRITE(ctrl->ports[left_pos], ctrl->pins[left_pos], 1);
  LED_PORT_WRITE(ctrl->ports[right_pos], ctrl->pins[right_pos], 1);
  
  /* 更新位置 */
  left_pos++;
  right_pos--;
  
  /* 如果相遇或错过，重新开始 */
  if (left_pos >= right_pos) {
    left_pos = 0;
    right_pos = LED_COUNT - 1;
  }
}

/* 阶梯递进模式 - 优化版本 */
static void led_mode_staircase(LedController *ctrl)
{
  static uint8_t stair_level = 0;
  static uint8_t stair_direction = 1;
  
  /* 熄灭所有LED */
  LED_ALL_OFF_FAST();
  
  /* 点亮到当前阶梯级别的所有LED - 使用模式优化 */
  uint16_t pattern = 0;
  for (uint8_t i = 0; i <= stair_level && i < LED_COUNT; i++) {
    pattern |= (1 << i);
  }
  led_batch_set_by_pattern(ctrl, pattern);
  
  /* 更新阶梯级别 */
  if (stair_direction) {
    stair_level++;
    if (stair_level >= LED_COUNT - 1) {
      stair_direction = 0;
    }
  } else {
    stair_level--;
    if (stair_level == 0) {
      stair_direction = 1;
    }
  }
}

/* 简单随机数生成器 */
static uint32_t simple_random(uint32_t *seed)
{
  *seed = (*seed * 1103515245 + 12345) & 0x7fffffff;
  return *seed;
}

